{% load static %}
<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <script src="{% static 'js/jquery.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="icon" href="{% static 'pic/Wilson_Blade_v9.png' %}" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="{% static 'css/style.css' %}">
    <title>CWSC Demo</title>
    <style>
        html {
            height: 100%;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f3f4f6;
            margin: 0;
            height: 100%;
        }

        header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        nav {
            display: flex;
            gap: 20px;
        }

        nav a {
            text-decoration: none;
            color: #333;
        }

        main {
            display: flex;
            /* 启用flex布局 */
            align-items: flex-start;
            /* 以防列表内容不足以填满高度 */
            height: calc(100vh - 65px);
            /* 保持您已设置的高度 */
            margin: 0;
            overflow: hidden;
            /* 隐藏溢出的内容 */
        }

        .list {
            width: 200px;
            /* 您已设置的固定宽度 */
            height: 100%;
            /* 使其高度填满main元素 */
            box-shadow: 5px 2px 3px rgba(0, 0, 1, 0.1);
            /* 不需要更改 */

        }

        .list .func {
            padding: 12px 10px;
            cursor: pointer;
            text-align: center;
            user-select: none;
            background-color: #f8f8f8;
            /* 背景颜色可以调整 */
            border-radius: 8px;
            /* 圆角 */
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            /* 给按钮添加一个轻微的阴影效果，创造凸起的感觉 */
            /* transition: all 0.3s ease; */
            transition: background-color 0.25s ease, box-shadow 0.25s ease, transform 0.25s ease;
            /* 添加过渡效果，使按钮变动更平滑 */
            margin-bottom: 15px;
            max-width: 160px;
            /* 限制按钮的最大宽度，防止背景过宽 */
            width: 95%;
            /* 默认宽度占父容器的100%，但不会超过最大宽度 */
            margin-left: auto;
            /* 水平居中 */
            margin-right: auto;
            /* 水平居中 */

        }

        .list .func:hover {
            background-color: palegoldenrod;
            /* 鼠标悬停时的背景色，可以根据需求调整 */
            box-shadow: 0px 8px 12px rgba(0, 0, 0, 0.15);
            /* 鼠标悬停时的阴影效果，凸起感更强 */
            transform: translateY(-5px);
            /* 鼠标悬停时按钮稍微上浮 */
        }

        .dis_func {
            flex-grow: 1;
            /* 新增，使其填满剩余空间 */
            height: 100%;
            /* 使其高度填满main元素 */
            /* 移除宽度设置，因为flex-grow将处理宽度 */
            margin-left: 20px;
            margin-right: 20px;
            overflow-y: auto;
        }
    </style>
</head>

<body>
    <header>
        <a class="logo" href="/" style="text-decoration: none;color: inherit;user-select: none;">
            <img src="{% static 'pic/Wilson_Blade_v9.png' %}" alt="图标" width="35" height="35" style="padding: 0">
            CWSC_Demo
        </a>
        <nav>
            <div id="log_status" style="cursor: pointer; margin-right: 30px;user-select: none;">

                {% if user.is_authenticated %}
                <a onclick="usage()">
                    {{ user.username }}
                </a>
                <img src="{% static 'pic/logout.png' %}" width="24" height="24" onclick="log_out()">


                {% else %}
                <div onclick="login()">注册/登录</div>
                {% endif %}
            </div>

            {# <div onclick="login()" id="log_status" style="cursor: pointer;margin-right: 30px">注册/登录</div>#}
            <!-- ... 更多链接 -->
        </nav>
    </header>
    <main>
        <div class="list">
            <div class="func" onclick="copywritting(this)" style="margin-top: 10px;">文案生成</div>
            <div class="func" onclick="checkCopywriting(this)">文案检查</div>
            <div class="func" onclick="optimize(this)">文案优化</div>
            <div class="func" onclick="changePlatform(this)">文案平台转换</div>
            <div class="func" onclick="filecontrol(this)">文档管理</div>
            <div class="func" onclick="chat(this)">Chat</div>
        </div>
        <div class="dis_func" id="show_place">


        </div>

        <!-- 登录模态对话框 -->
        <div class="modal fade" id="loginModal" tabindex="-1" role="dialog" aria-labelledby="loginModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="loginModalLabel" style="margin-left: calc(50% - 20px)">登录</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="loginForm">

                            <label for="username">用户名:</label>
                            <input type="text" class="form-control" id="username" autocomplete="username">
                            <label for="password" style="margin-top: 10px">密码:</label>
                            <input type="password" class="form-control" id="password" autocomplete="current-password">
                            <div id="regis_info" style="display: none">
                                <label for="registerpassword" style="margin-top: 10px">再次输入密码:</label>
                                <input type="password" class="form-control" id="registerpassword"
                                    autocomplete="new-password">
                                <label for="email" style="margin-top: 10px">邮箱:</label>
                                <input type="text" class="form-control" id="email" autocomplete="email">
                            </div>
                            <div style="margin-top: 20px;justify-content: center;display: flex">
                                <button type="button" class="btn btn-primary" id="log" onclick="login2()">登录</button>
                                <button type="button" class="btn btn-primary" style="display: none" id="reg"
                                    onclick="regis()">确认</button>
                                <button type="button" class="btn btn-secondary" style="margin-left: 10px" id="to_reg"
                                    onclick="toregis()">注册</button>
                                <button type="button" class="btn btn-secondary" style="margin-left: 10px;display: none"
                                    id="to_log" onclick="tolog()">返回</button>
                            </div>
                        </form>



                    </div>
                </div>
            </div>
        </div>

    </main>
</body>

<script>
    const socket = new WebSocket('ws://127.0.0.1:8000/ws/gpt/');
    let currentActiveButton = null; // 用来保存当前选中的按钮
    let messageBuffer = '';
    let show_place = $("#show_place")

    // 全局WebSocket消息处理器
    socket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        console.log('收到WebSocket消息:', data);
        
        // 尝试调用当前页面的消息处理函数
        if (typeof handleWebSocketMessage === 'function') {
            handleWebSocketMessage(data);
        }
        
        // 也可以根据target分发消息（备用方案）
        switch(data.target) {
            case 6: // 文案平台转换
                if (typeof handleWebSocketMessage === 'function') {
                    handleWebSocketMessage(data);
                }
                break;
            // 可以添加其他target的处理
            default:
                // 默认处理或传递给其他处理器
                break;
        }
    };

    socket.onopen = function(event) {
        console.log('全局WebSocket连接已建立');
    };

    socket.onclose = function(event) {
        console.log('全局WebSocket连接已关闭');
    };

    socket.onerror = function(error) {
        console.error('全局WebSocket错误:', error);
    };

    // 用于设置按钮的选中状态
    function setActive(button) {
        // 如果当前按钮已被选中，则不做任何处理
        if (currentActiveButton === button) {
            return;
        }

        // 1. 清除之前选中按钮的背景色
        if (currentActiveButton) {
            $(currentActiveButton).css('background-color', '');
            $(currentActiveButton).css('box-shadow', '0px 4px 8px rgba(0, 0, 0, 0.1)');
            $(currentActiveButton).css('transform', 'translateY(0)');
        }

        // 2. 为当前按钮添加选中的背景色
        $(button).css('background-color', 'palegoldenrod');
        $(button).css('box-shadow', '0px 8px 12px rgba(0, 0, 0, 0.15)');
        $(button).css('transform', 'translateY(-5px)');

        // 3. 更新 currentActiveButton 为当前点击的按钮
        currentActiveButton = button;
    }

    // 以下是功能函数调用，确保每个按钮点击时都会激活
    // <!-- 跳转文案撰写页面 -->
    function copywritting(button) {
        setActive(button); // 调用 setActive 设置选中按钮
        $.ajax({
            url: 'copywritting/',
            type: 'GET',
            dataType: 'html',
            success: function (data) {
                show_place.html(data);
            },
            error: function (error) {
                console.error('Failed to load content:', error);
            }
        });
    }
    // <!-- 跳转检查页面 -->
    function checkCopywriting(button) {
        setActive(button); // 调用 setActive 设置选中按钮
        $.ajax({
            url: 'checkCopywriting/',
            type: 'GET',
            dataType: 'html',
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (data) {
                show_place.html(data);
            },
            error: function (error) {
                console.error('Failed to load content:', error);
            }
        });
    }
    // < !--打开登录界面 -->
    function login() {
        $('#loginModal').modal('show');

    }
    // < !--执行登录操作 -->
    function login2() {
        username = $('#username').val();
        password = $('#password').val();
        $('#password').attr('autocomplete', 'current-password');
        userinfo = { "username": username, "password": password }
        $.ajax({
            url: 'login/',
            type: 'POST',
            dataType: 'json',
            data: userinfo,
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (data) {
                if (data.status === 1) {
                    window.location.href = '/';
                } else {
                    alert("账号密码错误")
                }
            },
            error: function (error) {
                alert("服务故障")
            }
        });
    }
    // < !--执行注册操作 -->
    function regis() {
        val1 = $('#password').val()
        val2 = $('#registerpassword').val()
        $('#password').attr('autocomplete', 'new-password');
        if (val1 != val2) {
            $('#registerpassword').css('border', '2px solid red');
            alert("两次输入不同");
        } else {
            $('#registerpassword').css('border', '1px solid #ced4da');
            userinfo = { "username": $('#username').val(), "password1": val1, "password2": val2, "email": $('#email').val() }
            $.ajax({
                url: 'register/',
                type: 'POST',
                data: userinfo,
                dataType: 'json',
                headers: {
                    'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
                },
                success: function (data) {
                    if (data.status > 0) {
                        alert("注册成功")
                        window.location.href = '/';
                    } else if (data.status === -3) {
                        alert("用户名重复")
                    } else if (data.status === -2) {
                        alert("密码太简单，请使用较为复杂的密码")
                    } else if (data.status === -1) {
                        alert("邮箱格式错误")
                    } else {
                        alert("请检查输入信息")
                    }
                },
                error: function (error) {
                    alert("服务故障")
                }
            });
        }
    }
    function showLoginForm() {
        $('#loginForm').show();
        $('#registerForm').hide();
    }

    function showRegisterForm() {
        $('#loginForm').hide();
        $('#registerForm').show();
    }
    // < !--执行注册界面 -->
    function toregis() {
        $('#regis_info').show()
        $('#log').hide()
        $('#to_log').show()
        $('#to_reg').hide()
        $('#reg').show()
    }
    function tolog() {
        $('#regis_info').hide()
        $('#log').show()
        $('#to_log').hide()
        $('#to_reg').show()
        $('#reg').hide()

    }
    // < !--跳转文档管理界面 -->
    function filecontrol(button) {
        setActive(button); // 调用 setActive 设置选中按钮
        $.ajax({
            url: 'filecontrol/',
            type: 'GET',
            dataType: 'html',
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (data) {
                show_place.html(data);
            },
            error: function (error) {
                console.error('Failed to load content:', error);
            }
        });
    }
    function toggleDropdown() {
        document.getElementById("dropdown-menu").classList.toggle("show");
    }

    function log_out() {
        $.ajax({
            url: 'logout/',
            type: 'GET',
            dataType: 'json',
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (data) {
                if (data.status === 1) {
                    window.location.href = "/"
                }
            },
            error: function (error) {
                alert("服务故障")
            }
        });
    }
    function usage() {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        window.open(`/get_usage?year=${year}&month=${month}`)
    }
    function optimize(button) {
        setActive(button); // 调用 setActive 设置选中按钮
        $.ajax({
            url: 'optimize/',
            type: 'GET',
            dataType: 'html',
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (data) {
                show_place.html(data);
            },
            error: function (error) {
                console.error('Failed to load content:', error);
            }
        });

    }
    function chat(button) {
        setActive(button); // 调用 setActive 设置选中按钮
        $.ajax({
            url: 'chat/',
            type: 'GET',
            dataType: 'html',
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (data) {
                show_place.html(data);
            },
            error: function (error) {
                console.error('Failed to load content:', error);
            }
        });

    }

    // <!-- 跳转文案平台转换页面 -->
    function changePlatform(button) {
        setActive(button); // 调用 setActive 设置选中按钮
        $.ajax({
            url: 'changePlatform/',
            type: 'GET',
            dataType: 'html',
            headers: {
                'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (data) {
                show_place.html(data);
            },
            error: function (error) {
                console.error('Failed to load content:', error);
            }
        });
    }


</script>

</html>