from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader, UnstructuredPowerPointLoader,UnstructuredWordDocumentLoader
import asyncio
import os
from typing import List, Dict, Optional


# UnstructuredPowerPointLoader 依赖 unstructured 库，
# 并且可能需要一些系统级别的依赖来处理复杂的PPTX结构。
# 确保安装：pip install unstructured pptx python-magic
# 如果遇到错误，特别是关于 "magic" 库的，尝试：pip install python-magic-bin (Windows) 或 brew install libmagic (macOS) / sudo apt-get install libmagic-dev (Ubuntu)

class LangchainDocumentExtractor:
    """
    一个使用 LangChain 异步提取 PDF、Word (docx) 和 PowerPoint (pptx) 文件文本内容的类。
    """

    async def _load_and_extract(self, loader) -> str:
        """
        内部辅助方法：异步加载文档并提取文本。
        """
        # LangChain 加载器通常是同步的，但我们可以在异步函数中调用它们，
        # 并可以考虑使用 run_in_executor 来避免阻塞（对于大型文件）。
        # 在此示例中，为简化起见，直接调用。
        documents = await asyncio.to_thread(loader.load)  # 使用 asyncio.to_thread 将同步操作变为非阻塞

        full_text = []
        for doc in documents:
            full_text.append(doc.page_content)
        return "\n".join(full_text)

    async def extract_text_from_doc_antiword(file_path):
        """使用antiword异步提取DOC文档文本"""
        loop = asyncio.get_event_loop()
        try:
            def sync_extract():
                import subprocess
                result = subprocess.run(['antiword', str(file_path)],
                                        capture_output=True, text=True)
                return result.stdout

            text = await loop.run_in_executor(None, sync_extract)
            return text
        except Exception as e:
            print(f"Error processing {file_path}: {str(e)}")
            return ""

    async def extract_text(self, file_path: str) -> Optional[str]:
        """
        根据文件扩展名，异步地从给定文件中提取文本。
        支持 PDF (.pdf)、Word (.docx) 和 PowerPoint (.pptx) 文件。
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return None

        file_extension = file_path.lower().split('.')[-1]
        loader = None

        if file_extension == "pdf":
            loader = PyPDFLoader(file_path)
            print(f"正在使用 PyPDFLoader 提取 PDF 文件: {file_path}")
        elif file_extension == "docx":
            loader = Docx2txtLoader(file_path)
            print(f"正在使用 Docx2txtLoader 提取 Word (.docx) 文件: {file_path}")
        elif file_extension == "pptx":
            # UnstructuredPowerPointLoader 通常比 python-pptx 库更强大，
            # 它可以提取更多结构化信息，但依赖也更多。
            # 如果只想提取纯文本，也可以直接使用 python-pptx 库（需要自己实现加载逻辑）
            loader = UnstructuredPowerPointLoader(file_path)
            print(f"正在使用 UnstructuredPowerPointLoader 提取 PowerPoint (.pptx) 文件: {file_path}")


        else:
            print(f"不支持的文件类型: {file_path}。目前支持 .pdf、.docx 和 .pptx。")
            return "目前支持的文件类型有pdf、docx 和 pptx"

        if loader:
            return await self._load_and_extract(loader)
        return None






async def main():
    extractor = LangchainDocumentExtractor()

    # # --- 替换为你的 PDF 文件路径 ---
    # pdf_file_path = r"D:\硕士\aiagent\gpt_CWSC\1.pdf"
    # print("\n--- 提取 PDF 文本 ---")
    # extracted_pdf_text = await extractor.extract_text(pdf_file_path)
    # if extracted_pdf_text:
    #     print("提取到的 PDF 文本（前500字符）：")
    #     print(extracted_pdf_text[:500])
    # else:
    #     print("PDF 文本提取失败或文件不存在。")
    #
    # # --- 替换为你的 Word (.docx) 文件路径 ---
    # docx_file_path =r"D:\硕士\aiagent\gpt_CWSC\1.docx"
    # print("\n--- 提取 Word (.docx) 文本 ---")
    # extracted_docx_text = await extractor.extract_text(docx_file_path)
    # if extracted_docx_text:
    #     print("提取到的 Word (.docx) 文本（前500字符）：")
    #     print(extracted_docx_text[:500])
    # else:
    #     print("Word (.docx) 文本提取失败或文件不存在。")
    #
    # # --- 替换为你的 PowerPoint (.pptx) 文件路径 ---
    # pptx_file_path = r"D:\硕士\aiagent\gpt_CWSC\1.pptx"
    # print("\n--- 提取 PowerPoint (.pptx) 文本 ---")
    # extracted_pptx_text = await extractor.extract_text(pptx_file_path)
    # if extracted_pptx_text:
    #     print("提取到的 PowerPoint (.pptx) 文本（前500字符）：")
    #     print(extracted_pptx_text[:500])
    # else:
    #     print("PowerPoint (.pptx) 文本提取失败或文件不存在。")

    # --- 替换为你的 Word (.doc) 文件路径 (仅限 Windows) ---
    doc_file_path = r"D:\硕士\aiagent\gpt_CWSC\1.doc"
    print("\n" + "=" * 30 + "\n")
    extracted_pdf_text = await extractor.extract_text(doc_file_path)
    if extracted_pdf_text:
        print("提取到的 PDF 文本（前500字符）：")
        print(extracted_pdf_text[:500])
    else:
        print("PDF 文本提取失败或文件不存在。")


    # --- 尝试提取不支持的文件类型 ---
    print("\n--- 尝试提取不支持的文件类型 ---")
    await extractor.extract_text("path/to/unsupported_file.txt")


if __name__ == "__main__":
    asyncio.run(main())