import pytz
from django.shortcuts import render
from style_content.models import CreatedContent
from style_content.tools.load_config import Config
# Create your views here.
config = Config()
models = config.get_config()["support_models"]
platforms = config.get_config()["platform_list"]
brands = config.get_config()["brand_list"]

def copywritting(request):
    context = {'models': models, 'platforms': platforms, 'brands': brands}
    return render(request, "index/copywritting.html", context)


def change_platform(request):
    return render(request, "index/change_platform.html")

def checkCopywriting(request):
    return render(request, "index/checkCopywriting.html")

def optimize(request):
    options = []
    if request.user.is_authenticated:
        records = CreatedContent.objects.filter(userid=request.user.id).values('id','Final_output', 'create_time')

        for i in records:
            beijing_tz = pytz.timezone('Asia/Shanghai')
            dt_beijing = i["create_time"].astimezone(beijing_tz)
            beijing_time_str = dt_beijing.strftime('%Y-%m-%d %H:%M:%S')
            options.append({"id": i["id"], "time": beijing_time_str, "info": i["Final_output"]})
    context = {"options":options}
    return render(request, "index/optimize.html", context)

def chat(request):
    return render(request, "index/chat.html")




