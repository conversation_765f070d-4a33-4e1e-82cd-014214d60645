<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="33a52aac-3a3a-4051-b254-aa8fee5cfedc" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DjangoConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)">
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/V1addOA-concisePrompt.jsonl" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2nyjRkTWpyZKTI2PhtqSYj7BTER" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.1.executor&quot;: &quot;Run&quot;,
    &quot;Python.Entity_claim.executor&quot;: &quot;Run&quot;,
    &quot;Python.Multimodal_gemini.executor&quot;: &quot;Run&quot;,
    &quot;Python.agno_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.bocha_search.executor&quot;: &quot;Run&quot;,
    &quot;Python.coalition_value.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.main_new.executor&quot;: &quot;Run&quot;,
    &quot;Python.selfask.executor&quot;: &quot;Run&quot;,
    &quot;Python.test (1).executor&quot;: &quot;Debug&quot;,
    &quot;Python.test.executor&quot;: &quot;Run&quot;,
    &quot;Python.test1.executor&quot;: &quot;Run&quot;,
    &quot;Python.test2 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.test2.executor&quot;: &quot;Run&quot;,
    &quot;Python.test3.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_demo.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_demo2.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_demo_chaumet.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_demo_lv.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_wendang.executor&quot;: &quot;Run&quot;,
    &quot;Python.testopenaiapi.executor&quot;: &quot;Run&quot;,
    &quot;Python.use_deepseek.executor&quot;: &quot;Run&quot;,
    &quot;Python.use_gemini.executor&quot;: &quot;Run&quot;,
    &quot;Python.use_mulmola_gemini.executor&quot;: &quot;Run&quot;,
    &quot;Python.useglm4flash.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.OpenDjangoStructureViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/硕士/方向论文/pythonProject&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\硕士\aiagent\gpt_CWSC\example\content" />
      <recent name="D:\硕士\aiagent\gpt_CWSC" />
      <recent name="D:\硕士\aiagent\gpt_CWSC\DAO\Casual_graph" />
      <recent name="D:\硕士\aiagent\gpt_CWSC\DAO\DAG" />
      <recent name="D:\硕士\aiagent\gpt_CWSC\example" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\硕士\aiagent\gpt_CWSC\DAO\DAG" />
    </key>
  </component>
  <component name="RunManager" selected="Python.test_wendang">
    <configuration name="test_demo" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gpt_CWSC" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_demo.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_demo_chaumet" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gpt_CWSC" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_demo_chaumet.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_demo_lv" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gpt_CWSC" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_demo_lv.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_wendang" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gpt_CWSC" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_wendang.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="use_mulmola_gemini" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gpt_CWSC" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/example" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/example/use_mulmola_gemini.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.test_wendang" />
        <item itemvalue="Python.test_demo_chaumet" />
        <item itemvalue="Python.use_mulmola_gemini" />
        <item itemvalue="Python.test_demo_lv" />
        <item itemvalue="Python.test_demo" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-50da183f06c8-2887949eec09-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-233.13135.95" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="33a52aac-3a3a-4051-b254-aa8fee5cfedc" name="更改" comment="" />
      <created>1729952203888</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1729952203888</updated>
      <workItem from="1729952204971" duration="23005000" />
      <workItem from="1730170119608" duration="1124000" />
      <workItem from="1730171433409" duration="65040000" />
      <workItem from="1730299633713" duration="307000" />
      <workItem from="1730337562217" duration="15402000" />
      <workItem from="1730367106274" duration="24607000" />
      <workItem from="1730448930511" duration="49000" />
      <workItem from="1730449211112" duration="167000" />
      <workItem from="1730449397643" duration="2795000" />
      <workItem from="1730452276733" duration="116000" />
      <workItem from="1730452431591" duration="633000" />
      <workItem from="1730458073320" duration="12000" />
      <workItem from="1730458194413" duration="163000" />
      <workItem from="1730460735445" duration="28000" />
      <workItem from="1730460772947" duration="18000" />
      <workItem from="1730460812387" duration="51000" />
      <workItem from="1730974403231" duration="17043000" />
      <workItem from="1731026687831" duration="2967000" />
      <workItem from="1731033339005" duration="19466000" />
      <workItem from="1731382114266" duration="21000" />
      <workItem from="1731382190426" duration="66000" />
      <workItem from="1731383063353" duration="1430000" />
      <workItem from="1731384555831" duration="8645000" />
      <workItem from="1732948087898" duration="9528000" />
      <workItem from="1733578561514" duration="1311000" />
      <workItem from="1733897149329" duration="1058000" />
      <workItem from="1741849669592" duration="967000" />
      <workItem from="1743162764700" duration="1931000" />
      <workItem from="1743483782495" duration="355000" />
      <workItem from="1743484617442" duration="8438000" />
      <workItem from="1743565103679" duration="1454000" />
      <workItem from="1743566567596" duration="6002000" />
      <workItem from="1743662775905" duration="486000" />
      <workItem from="1744018281692" duration="615000" />
      <workItem from="1744107836585" duration="29000" />
      <workItem from="1744438746928" duration="8558000" />
      <workItem from="1744525195682" duration="157000" />
      <workItem from="1744527906359" duration="5428000" />
      <workItem from="1744598938912" duration="6612000" />
      <workItem from="1744718830645" duration="1201000" />
      <workItem from="1744870250970" duration="4436000" />
      <workItem from="1745043604388" duration="8989000" />
      <workItem from="1745207828949" duration="619000" />
      <workItem from="1745291446846" duration="561000" />
      <workItem from="1745301874663" duration="412000" />
      <workItem from="1745384511363" duration="2114000" />
      <workItem from="1745395965274" duration="1181000" />
      <workItem from="1745412315672" duration="1217000" />
      <workItem from="1745415634525" duration="1094000" />
      <workItem from="1745469695126" duration="2251000" />
      <workItem from="1745481568043" duration="724000" />
      <workItem from="1745577192721" duration="48000" />
      <workItem from="1745584130281" duration="1488000" />
      <workItem from="1745643129150" duration="623000" />
      <workItem from="1745725996642" duration="20571000" />
      <workItem from="1745996646060" duration="2475000" />
      <workItem from="1746252282655" duration="27000" />
      <workItem from="1746703162466" duration="771000" />
      <workItem from="1746769807089" duration="1224000" />
      <workItem from="1746946399220" duration="12000" />
      <workItem from="1747039852498" duration="28203000" />
      <workItem from="1747307163350" duration="214000" />
      <workItem from="1747307392751" duration="23581000" />
      <workItem from="1748070576911" duration="2645000" />
      <workItem from="1748150364509" duration="17377000" />
      <workItem from="1748254582618" duration="622000" />
      <workItem from="1748333050546" duration="5130000" />
      <workItem from="1748697043579" duration="523000" />
      <workItem from="1748839967398" duration="3503000" />
      <workItem from="1748849981860" duration="2809000" />
      <workItem from="1749099116162" duration="606000" />
      <workItem from="1749121785337" duration="67000" />
      <workItem from="1749128333209" duration="4750000" />
      <workItem from="1749547565063" duration="1247000" />
      <workItem from="1749613387595" duration="44000" />
      <workItem from="1749967722473" duration="729000" />
      <workItem from="1749978849065" duration="1265000" />
      <workItem from="1750073528601" duration="915000" />
      <workItem from="1750150182618" duration="8477000" />
      <workItem from="1750225453192" duration="16385000" />
      <workItem from="1750323757379" duration="11283000" />
      <workItem from="1750399130566" duration="7849000" />
      <workItem from="1750668862528" duration="4379000" />
      <workItem from="1750749201986" duration="21028000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/DAO/DAG/test2.py</url>
          <line>521</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/test_demo.py</url>
          <line>147</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/gpt_CWSC$Multimodal_gemini.coverage" NAME="Multimodal_gemini 覆盖结果" MODIFIED="1750331880882" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test__1_.coverage" NAME="test (1) 覆盖结果" MODIFIED="1747040268455" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/Casual_graph" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test2__1_.coverage" NAME="test2 (1) 覆盖结果" MODIFIED="1747310014662" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAG" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$use_deepseek.coverage" NAME="use_deepseek 覆盖结果" MODIFIED="1748850006679" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/example" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test_demo_lv.coverage" NAME="test_demo_lv 覆盖结果" MODIFIED="1750405517460" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test.coverage" NAME="test 覆盖结果" MODIFIED="1747382622089" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/Casual_graph" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$use_mulmola_gemini.coverage" NAME="use_mulmola_gemini 覆盖结果" MODIFIED="1750749236908" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/example" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$useglm4flash.coverage" NAME="useglm4flash 覆盖结果" MODIFIED="1748336235436" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/example" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test2.coverage" NAME="test2 覆盖结果" MODIFIED="1745836852296" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAO" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$bocha_search.coverage" NAME="bocha_search 覆盖结果" MODIFIED="1743567045018" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$main.coverage" NAME="main 覆盖结果" MODIFIED="1745761055289" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAO/utils/prompt" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$coalition_value.coverage" NAME="coalition_value 覆盖结果" MODIFIED="1747372102101" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAG" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$main_new.coverage" NAME="main_new 覆盖结果" MODIFIED="1745730574489" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAO/utils/prompt" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test3.coverage" NAME="test3 覆盖结果" MODIFIED="1747309887425" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAG" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test_demo.coverage" NAME="test_demo 覆盖结果" MODIFIED="1750402559072" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test_wendang.coverage" NAME="test_wendang 覆盖结果" MODIFIED="1750991932595" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$1.coverage" NAME="1 覆盖结果" MODIFIED="1747372187885" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAG" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test_demo2.coverage" NAME="test_demo2 覆盖结果" MODIFIED="1748234032999" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test_demo_chaumet.coverage" NAME="test_demo_chaumet 覆盖结果" MODIFIED="1750750977524" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$selfask.coverage" NAME="selfask 覆盖结果" MODIFIED="1743493091245" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/example" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$test1.coverage" NAME="test1 覆盖结果" MODIFIED="1747118920283" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO/DAG" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$use_gemini.coverage" NAME="use_gemini 覆盖结果" MODIFIED="1750226580724" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/example" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$agno_test.coverage" NAME="agno_test 覆盖结果" MODIFIED="1745741116348" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/example" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$Entity_claim.coverage" NAME="Entity_claim 覆盖结果" MODIFIED="1745589341665" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/DAO" />
    <SUITE FILE_PATH="coverage/gpt_CWSC$testopenaiapi.coverage" NAME="testopenaiapi 覆盖结果" MODIFIED="1750226975428" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/example" />
  </component>
</project>