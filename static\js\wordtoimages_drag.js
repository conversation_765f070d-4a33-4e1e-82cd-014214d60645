// wordtoimages_drag.js - 专门用于文字转图片查询页面的拖拽上传功能

// 检查是否存在 image-description 元素
if (document.getElementById('image-description')) {
    
    document.getElementById('image-description').addEventListener('dragover', function(event) {
        event.stopPropagation();
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
        this.classList.add('dragover');
    });

    document.getElementById('image-description').addEventListener('dragleave', function(event) {
        this.classList.remove('dragover');
    });

    var uploadedFile;
    document.getElementById('image-description').addEventListener('drop', function(event) {
        event.stopPropagation();
        event.preventDefault();
        this.classList.remove('dragover');
        var files = event.dataTransfer.files;
        
        if (files.length > 1) {
            alert('每次只能上传一个文件！');
            return;
        }

        uploadedFile = files[0]; // 因为只允许一个文件，所以直接取第一个

        // 检查文件类型
        if (!uploadedFile.type.startsWith('image/')) {
            alert('请上传图片文件！');
            return;
        }

        if (uploadedFile.size > 52428800) { // 50MB 的字节数
            alert('文件大小不能超过50MB！');
            return;
        }
        
        $('#image-description').prop('disabled', true);
        $('#image-description').attr('placeholder', '');
        $('#image-description').css("background-color","#e0e0e0");
        $("#select-mode").css("display","block");
    });

    // 拖拽信息处理函数
    window.draginfo = function() {
        $('#image-description').attr('placeholder', '请在此处填写图片相关信息,可拖动图片文件至此窗口以识别图片内部信息');
        $("#select-mode").css("display","none");
        $('#loading-spinner').css("display","inline-block");
        
        // 将文件转换为base64格式
        var reader = new FileReader();
        reader.onload = function(e) {
            var imageData = {
                id: Date.now(),
                name: uploadedFile.name,
                dataUrl: e.target.result
            };
            
            // 将图片数据添加到全局变量中
            if (typeof uploadedImages !== 'undefined') {
                uploadedImages = [imageData]; // 替换之前的图片
                updateImageStats();
            }
            
            // 在文本框中显示文件信息
            $('#image-description').val('已上传图片: ' + uploadedFile.name);
            
            $('#loading-spinner').hide();
            $('#image-description').prop('disabled', false);
            $('#image-description').css("background-color", "");
            
            uploadedFile = null;
        };
        
        reader.onerror = function() {
            $('#loading-spinner').hide();
            $('#image-description').prop('disabled', false);
            $('#image-description').css("background-color", "");
            alert('文件读取失败，请重试');
            uploadedFile = null;
        };
        
        reader.readAsDataURL(uploadedFile);
    };
}
