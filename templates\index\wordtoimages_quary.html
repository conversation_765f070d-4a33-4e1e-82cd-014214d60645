{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字转图片查询</title>
    <style>
        .container {
            max-width: 90%;
            margin-top: 15px;
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        .header-title {
            text-align: center;
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .input-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .section-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .text-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
            font-family: 'Arial', sans-serif;
        }

        .text-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .image-upload-area {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background: #ffffff;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .image-upload-area:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .image-upload-area.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        .upload-icon {
            font-size: 48px;
            color: #bdc3c7;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #95a5a6;
            font-size: 12px;
        }

        .image-preview {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .model-select {
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .model-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .submit-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .submit-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading-indicator {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            display: none;
        }

        .result-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            white-space: pre-wrap;
            line-height: 1.6;
        }

        .copy-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 10px;
            transition: background 0.3s ease;
        }

        .copy-btn:hover {
            background: #219a52;
        }

        .file-input {
            display: none;
        }

        .remove-image-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 12px;
        }

        /* 新增美化样式 */
        .feature-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-highlight h3 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }

        .feature-highlight p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #ecf0f1;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .tip-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }

        .tip-box .tip-icon {
            color: #f39c12;
            margin-right: 8px;
        }

        .tip-box .tip-text {
            color: #856404;
            font-size: 14px;
            margin: 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            .stats-container {
                flex-direction: column;
                gap: 10px;
            }

            .header-title {
                font-size: 20px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .input-section {
            animation: fadeInUp 0.6s ease-out;
        }

        .input-section:nth-child(2) { animation-delay: 0.1s; }
        .input-section:nth-child(3) { animation-delay: 0.2s; }
        .input-section:nth-child(4) { animation-delay: 0.3s; }

        /* 悬浮效果 */
        .input-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* 成功状态样式 */
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header-title">
            📝 文字转图片查询分析
        </div>

        <!-- 功能介绍 -->
        <div class="feature-highlight">
            <h3>🚀 智能分析助手</h3>
            <p>结合AI技术，深度分析文字描述与图片内容的关联性，为您提供专业的创意建议和优化方案</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-container">
            <div class="stat-item">
                <span class="stat-number" id="textLength">0</span>
                <div class="stat-label">文字字数</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="imageCount">0</span>
                <div class="stat-label">图片数量</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="analysisTime">0</span>
                <div class="stat-label">分析时间(秒)</div>
            </div>
        </div>

        <!-- 提示信息 -->
        <div class="tip-box">
            <span class="tip-icon">💡</span>
            <p class="tip-text">
                <strong>使用提示：</strong>
                您可以单独输入文字进行分析，或上传图片进行视觉分析，也可以同时提供文字和图片获得更全面的分析结果。
            </p>
        </div>

        <!-- 模型选择 -->
        <div class="input-section">
            <div class="section-title">🤖 模型选择</div>
            <select id="modelSelect" class="model-select">
                {% for model in models %}
                <option value="{{ model.name }}">{{ model.name }}</option>
                {% endfor %}
            </select>
        </div>

        <!-- 文字输入区域 -->
        <div class="input-section">
            <div class="section-title">✍️ 文字描述</div>
            <textarea 
                id="textInput" 
                class="text-input" 
                placeholder="请输入您想要分析的文字内容，例如：&#10;- 产品描述&#10;- 场景描述&#10;- 创意想法&#10;- 或任何您想要转换为图片的文字内容..."
            ></textarea>
        </div>

        <!-- 图片上传区域 -->
        <div class="input-section">
            <div class="section-title">🖼️ 图片上传（可选）</div>
            <div id="imageUploadArea" class="image-upload-area">
                <div class="upload-icon">📷</div>
                <div class="upload-text">点击或拖拽图片到此处</div>
                <div class="upload-hint">支持 JPG、PNG、GIF 格式，最大 10MB</div>
                <input type="file" id="fileInput" class="file-input" accept="image/*" multiple>
            </div>
            <div id="imagePreview" class="image-preview">
                <div id="previewContainer"></div>
                <button id="removeImageBtn" class="remove-image-btn" style="display: none;">×</button>
            </div>
        </div>

        <!-- 提交按钮 -->
        <button id="submitBtn" class="submit-btn">
            🚀 开始分析
        </button>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="spinner"></div>
            <div>正在分析中，请稍候...</div>
        </div>

        <!-- 结果显示区域 -->
        <div id="resultSection" class="result-section">
            <div class="section-title">📊 分析结果</div>
            <div id="resultContent" class="result-content"></div>
            <button id="copyBtn" class="copy-btn">📋 复制结果</button>
        </div>
    </div>

    <script>
        // 全局变量
        let uploadedImages = [];
        let socket = null;
        let analysisStartTime = 0;

        // 初始化WebSocket连接
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/gpt/`;
            
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function(event) {
                console.log('WebSocket连接已建立');
            };
            
            socket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            socket.onclose = function(event) {
                console.log('WebSocket连接已关闭');
            };
            
            socket.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            if (data.target === 7) { // 新的target值用于文字转图片查询
                if (data.message) {
                    const resultContent = document.getElementById('resultContent');
                    resultContent.innerHTML += data.message;
                }

                if (data.status === 1) {
                    // 分析完成
                    document.getElementById('loadingIndicator').style.display = 'none';
                    document.getElementById('submitBtn').disabled = false;
                    document.getElementById('resultSection').style.display = 'block';

                    // 停止计时
                    analysisStartTime = 0;

                    // 显示成功消息
                    showSuccessMessage('分析完成！');
                } else if (data.status === 0) {
                    // 分析失败
                    document.getElementById('loadingIndicator').style.display = 'none';
                    document.getElementById('submitBtn').disabled = false;
                    analysisStartTime = 0;
                    alert('分析失败，请重试');
                }
            }
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.innerHTML = `✅ ${message}`;

            const container = document.querySelector('.container');
            container.insertBefore(successDiv, document.getElementById('resultSection'));

            // 3秒后自动移除
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            initEventListeners();
        });

        // 初始化事件监听器
        function initEventListeners() {
            const imageUploadArea = document.getElementById('imageUploadArea');
            const fileInput = document.getElementById('fileInput');
            const submitBtn = document.getElementById('submitBtn');
            const copyBtn = document.getElementById('copyBtn');
            const textInput = document.getElementById('textInput');

            // 图片上传区域点击事件
            imageUploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择事件
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            imageUploadArea.addEventListener('dragover', handleDragOver);
            imageUploadArea.addEventListener('dragleave', handleDragLeave);
            imageUploadArea.addEventListener('drop', handleDrop);

            // 提交按钮事件
            submitBtn.addEventListener('click', submitAnalysisRequest);

            // 复制按钮事件
            copyBtn.addEventListener('click', copyResult);

            // 文字输入实时统计
            textInput.addEventListener('input', updateTextStats);
        }

        // 更新文字统计
        function updateTextStats() {
            const textInput = document.getElementById('textInput');
            const textLength = textInput.value.length;
            document.getElementById('textLength').textContent = textLength;
        }

        // 更新图片统计
        function updateImageStats() {
            document.getElementById('imageCount').textContent = uploadedImages.length;
        }

        // 更新分析时间
        function updateAnalysisTime() {
            if (analysisStartTime > 0) {
                const currentTime = Math.floor((Date.now() - analysisStartTime) / 1000);
                document.getElementById('analysisTime').textContent = currentTime;
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            processFiles(files);
        }

        // 处理拖拽悬停
        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.add('dragover');
        }

        // 处理拖拽离开
        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.remove('dragover');
        }

        // 处理拖拽放下
        function handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.remove('dragover');
            
            const files = Array.from(event.dataTransfer.files);
            processFiles(files);
        }

        // 处理文件
        function processFiles(files) {
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length === 0) {
                alert('请选择图片文件');
                return;
            }

            // 限制文件大小
            const maxSize = 10 * 1024 * 1024; // 10MB
            const oversizedFiles = imageFiles.filter(file => file.size > maxSize);
            
            if (oversizedFiles.length > 0) {
                alert('部分文件超过10MB限制，已被忽略');
                imageFiles = imageFiles.filter(file => file.size <= maxSize);
            }

            // 处理图片文件
            imageFiles.forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageData = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        dataUrl: e.target.result
                    };
                    uploadedImages.push(imageData);
                    displayImagePreview(imageData);
                };
                reader.readAsDataURL(file);
            });
        }

        // 显示图片预览
        function displayImagePreview(imageData) {
            const previewContainer = document.getElementById('previewContainer');
            const imagePreview = document.getElementById('imagePreview');

            const imgElement = document.createElement('img');
            imgElement.src = imageData.dataUrl;
            imgElement.className = 'preview-image';
            imgElement.style.marginBottom = '10px';

            previewContainer.appendChild(imgElement);
            imagePreview.style.display = 'block';

            // 显示删除按钮
            document.getElementById('removeImageBtn').style.display = 'block';
            document.getElementById('removeImageBtn').onclick = clearImages;

            // 更新图片统计
            updateImageStats();
        }

        // 清除图片
        function clearImages() {
            uploadedImages = [];
            document.getElementById('previewContainer').innerHTML = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('fileInput').value = '';
            updateImageStats();
        }

        // 提交分析请求
        function submitAnalysisRequest() {
            const textInput = document.getElementById('textInput').value.trim();
            const model = document.getElementById('modelSelect').value;

            if (!textInput && uploadedImages.length === 0) {
                alert('请输入文字描述或上传图片');
                return;
            }

            if (!socket || socket.readyState !== WebSocket.OPEN) {
                alert('连接失败，请刷新页面重试');
                return;
            }

            // 禁用提交按钮并显示加载指示器
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';

            // 开始计时
            analysisStartTime = Date.now();
            document.getElementById('analysisTime').textContent = '0';

            // 启动计时器
            const timer = setInterval(() => {
                updateAnalysisTime();
            }, 1000);

            // 构建请求数据
            const requestData = {
                source: 7, // 新的source值用于文字转图片查询
                model: model,
                textContent: textInput,
                uploadedImages: uploadedImages,
                imageAnalysisRequest: '分析图片内容并结合文字描述提供详细的分析结果'
            };

            // 发送请求
            socket.send(JSON.stringify(requestData));
        }

        // 复制结果
        function copyResult() {
            const resultContent = document.getElementById('resultContent');
            const textToCopy = resultContent.textContent || resultContent.innerText;
            
            navigator.clipboard.writeText(textToCopy).then(function() {
                alert('结果已复制到剪贴板');
            }).catch(function(error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动选择复制');
            });
        }
    </script>
</body>

</html>
